<?php

declare(strict_types=1);

namespace Drupal\lh\Service;

use Drupal\Core\File\FileSystemInterface;
use <PERSON>upal\Core\Logger\LoggerChannelInterface;

/**
 * Service for generating chart images using Chart.js and headless browser.
 */
class ChartImageGenerator {

  public function __construct(
    private readonly FileSystemInterface $fileSystem,
    private readonly LoggerChannelInterface $logger,
  ) {}

  /**
   * Generates a chart image from statistics data.
   *
   * @param array $statistics
   *   The statistics data array.
   * @param string $title
   *   The title for the chart.
   *
   * @return string|null
   *   The file path of the generated image, or NULL on failure.
   */
  public function generateChartImage(array $statistics, string $title = '📊 下注统计报告'): ?string {
    try {
      // Create HTML file with Chart.js
      $html_content = $this->generateChartHtml($statistics, $title);
      $html_file = $this->saveTemporaryHtml($html_content);
      
      if (!$html_file) {
        return NULL;
      }

      // Generate image using headless browser
      $image_path = $this->convertHtmlToImage($html_file);
      
      // Clean up temporary HTML file
      unlink($html_file);
      
      return $image_path;

    } catch (\Exception $e) {
      $this->logger->error('Error generating chart image: @message', ['@message' => $e->getMessage()]);
      return NULL;
    }
  }

  /**
   * Generates HTML content with Chart.js.
   */
  private function generateChartHtml(array $statistics, string $title): string {
    $chart_data = $this->prepareChartData($statistics);
    
    return <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Statistics Chart</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            margin: 20px; 
            background: #f8f9fa;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title { 
            text-align: center; 
            color: #2c3e50; 
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: bold;
        }
        .chart-container { 
            position: relative; 
            height: 600px; 
            margin-bottom: 20px;
        }
        .stats-summary {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            padding: 15px;
            background: #ecf0f1;
            border-radius: 8px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #e74c3c;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">{$title}</div>
        
        <div class="stats-summary">
            <div class="stat-item">
                <div class="stat-value">{$chart_data['overall']['wager_count']}</div>
                <div class="stat-label">总下注次数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{$chart_data['overall']['total_amount']}元</div>
                <div class="stat-label">总金额</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{$chart_data['overall']['total_bets']}</div>
                <div class="stat-label">总下注项</div>
            </div>
        </div>
        
        <div class="chart-container">
            <canvas id="statisticsChart"></canvas>
        </div>
    </div>

    <script>
        const ctx = document.getElementById('statisticsChart').getContext('2d');
        const chartData = {$this->jsonEncode($chart_data)};
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: chartData.labels,
                datasets: [{
                    data: chartData.data,
                    backgroundColor: [
                        '#3498db', '#e74c3c', '#2ecc71', '#f39c12', 
                        '#9b59b6', '#1abc9c', '#34495e', '#e67e22'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 14,
                                family: 'Microsoft YaHei'
                            },
                            padding: 20
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '元';
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
HTML;
  }

  /**
   * Prepares chart data from statistics.
   */
  private function prepareChartData(array $statistics): array {
    $labels = [];
    $data = [];
    
    // Add regional data
    if (isset($statistics['macau']) && $statistics['macau']['total_amount'] > 0) {
      $labels[] = '🇲🇴 澳门地区';
      $data[] = $statistics['macau']['total_amount'];
    }
    
    if (isset($statistics['hongkong']) && $statistics['hongkong']['total_amount'] > 0) {
      $labels[] = '🇭🇰 香港地区';
      $data[] = $statistics['hongkong']['total_amount'];
    }
    
    return [
      'labels' => $labels,
      'data' => $data,
      'overall' => $statistics['overall'] ?? ['wager_count' => 0, 'total_amount' => 0, 'total_bets' => 0]
    ];
  }

  /**
   * Saves HTML content to temporary file.
   */
  private function saveTemporaryHtml(string $content): ?string {
    $temp_dir = sys_get_temp_dir();
    $filename = 'chart_' . uniqid() . '.html';
    $filepath = $temp_dir . DIRECTORY_SEPARATOR . $filename;
    
    if (file_put_contents($filepath, $content) !== false) {
      return $filepath;
    }
    
    return NULL;
  }

  /**
   * Converts HTML to image using headless browser.
   */
  private function convertHtmlToImage(string $html_file): ?string {
    $filename = 'chart_' . date('YmdHis') . '_' . uniqid() . '.png';
    $directory = 'public://lh/charts';
    $this->fileSystem->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY);
    $output_path = $this->fileSystem->realpath($directory) . DIRECTORY_SEPARATOR . $filename;
    
    // Try different methods to convert HTML to image
    $methods = [
      $this->tryPuppeteer($html_file, $output_path),
      $this->tryWkhtmltoimage($html_file, $output_path),
      $this->tryPhantomJS($html_file, $output_path),
    ];
    
    foreach ($methods as $success) {
      if ($success && file_exists($output_path)) {
        return $output_path;
      }
    }
    
    return NULL;
  }

  /**
   * Try using Puppeteer to convert HTML to image.
   */
  private function tryPuppeteer(string $html_file, string $output_path): bool {
    $command = "node -e \"
      const puppeteer = require('puppeteer');
      (async () => {
        const browser = await puppeteer.launch({headless: true});
        const page = await browser.newPage();
        await page.setViewport({width: 1200, height: 800});
        await page.goto('file://{$html_file}');
        await page.waitForTimeout(2000);
        await page.screenshot({path: '{$output_path}', fullPage: true});
        await browser.close();
      })();
    \"";
    
    exec($command, $output, $return_code);
    return $return_code === 0;
  }

  /**
   * Try using wkhtmltoimage to convert HTML to image.
   */
  private function tryWkhtmltoimage(string $html_file, string $output_path): bool {
    $command = "wkhtmltoimage --width 1200 --height 800 --javascript-delay 3000 '{$html_file}' '{$output_path}'";
    exec($command, $output, $return_code);
    return $return_code === 0;
  }

  /**
   * Try using PhantomJS to convert HTML to image.
   */
  private function tryPhantomJS(string $html_file, string $output_path): bool {
    // PhantomJS script would go here
    return false;
  }

  /**
   * JSON encode with proper Unicode handling.
   */
  private function jsonEncode($data): string {
    return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
  }
}
