<?php

declare(strict_types=1);

namespace Drupal\lh\Service;

use Drupal\Core\File\FileSystemInterface;
use <PERSON>upal\Core\Image\ImageFactory;
use <PERSON>upal\Core\Logger\LoggerChannelInterface;

/**
 * Service for generating statistics images using Drupal Image API.
 */
class StatisticsImageGenerator {

  public function __construct(
    private readonly ImageFactory $imageFactory,
    private readonly FileSystemInterface $fileSystem,
    private readonly LoggerChannelInterface $logger,
  ) {}

  /**
   * Generates a statistics image from data array.
   *
   * @param array $statistics
   *   The statistics data array.
   * @param string $title
   *   The title for the image.
   *
   * @return string|null
   *   The file path of the generated image, or NULL on failure.
   */
  public function generateStatisticsImage(array $statistics, string $title = '📊 下注统计报告'): ?string {
    try {
      // Calculate image dimensions based on content
      $content_lines = $this->prepareContentLines($statistics, $title);
      $image_width = 800;
      $image_height = max(600, count($content_lines) * 35 + 100);

      // Create new image
      $image = $this->imageFactory->get();
      if (!$image->createNew($image_width, $image_height, 'png', '#ffffff')) {
        $this->logger->error('Failed to create new image');
        return NULL;
      }

      // Get GD resource for direct manipulation
      $gd_resource = $image->getToolkit()->getImage();
      
      // Generate the image content
      $this->drawImageContent($gd_resource, $content_lines, $image_width, $image_height);

      // Save the image
      $filename = 'statistics_' . date('YmdHis') . '_' . uniqid() . '.png';
      $directory = 'public://lh/statistics';
      $this->fileSystem->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY);
      $file_path = $directory . '/' . $filename;

      if ($image->save($file_path)) {
        $this->logger->info('Statistics image generated successfully: @path', ['@path' => $file_path]);
        return $this->fileSystem->realpath($file_path);
      }

      $this->logger->error('Failed to save statistics image');
      return NULL;

    } catch (\Exception $e) {
      $this->logger->error('Error generating statistics image: @message', ['@message' => $e->getMessage()]);
      return NULL;
    }
  }

  /**
   * Prepares content lines from statistics data.
   */
  private function prepareContentLines(array $statistics, string $title): array {
    $lines = [];
    $lines[] = ['text' => $title, 'size' => 24, 'color' => '#2c3e50', 'bold' => true];
    $lines[] = ['text' => str_repeat('─', 50), 'size' => 16, 'color' => '#bdc3c7'];

    // Overall statistics
    if (isset($statistics['overall'])) {
      $overall = $statistics['overall'];
      $lines[] = ['text' => '📈 总体统计', 'size' => 20, 'color' => '#e74c3c', 'bold' => true];
      $lines[] = ['text' => "总下注次数：{$overall['wager_count']}次", 'size' => 16, 'color' => '#34495e'];
      $lines[] = ['text' => "总金额：{$overall['total_amount']}元", 'size' => 16, 'color' => '#34495e'];
      $lines[] = ['text' => '', 'size' => 12]; // Empty line
    }

    // Regional statistics
    $regions = [
      'macau' => ['name' => '🇲🇴 澳门地区', 'color' => '#27ae60'],
      'hongkong' => ['name' => '🇭🇰 香港地区', 'color' => '#3498db']
    ];

    foreach ($regions as $region_key => $region_info) {
      if (isset($statistics[$region_key]) && $statistics[$region_key]['wager_count'] > 0) {
        $region_data = $statistics[$region_key];
        
        $lines[] = ['text' => $region_info['name'], 'size' => 18, 'color' => $region_info['color'], 'bold' => true];
        $lines[] = ['text' => "下注次数：{$region_data['wager_count']}次", 'size' => 14, 'color' => '#34495e'];
        $lines[] = ['text' => "金额：{$region_data['total_amount']}元", 'size' => 14, 'color' => '#34495e'];

        // Numbers statistics
        if (!empty($region_data['numbers'])) {
          $lines[] = ['text' => '🔢 号码统计：', 'size' => 16, 'color' => '#8e44ad', 'bold' => true];
          $number_chunks = array_chunk($region_data['numbers'], 6, true);
          foreach ($number_chunks as $chunk) {
            $number_line = '';
            foreach ($chunk as $number => $amount) {
              $number_line .= "  {$number}号：{$amount}元";
            }
            $lines[] = ['text' => $number_line, 'size' => 14, 'color' => '#34495e'];
          }
        }

        // Animals statistics
        if (!empty($region_data['animals'])) {
          $lines[] = ['text' => '🐾 生肖统计：', 'size' => 16, 'color' => '#f39c12', 'bold' => true];
          $animal_chunks = array_chunk($region_data['animals'], 4, true);
          foreach ($animal_chunks as $chunk) {
            $animal_line = '';
            foreach ($chunk as $animal => $amount) {
              $animal_line .= "  {$animal}（各数）：{$amount}元";
            }
            $lines[] = ['text' => $animal_line, 'size' => 14, 'color' => '#34495e'];
          }
        }

        $lines[] = ['text' => '', 'size' => 12]; // Empty line
      }
    }

    $lines[] = ['text' => '📊 统计完成 - ' . date('Y-m-d H:i:s'), 'size' => 14, 'color' => '#95a5a6'];

    return $lines;
  }

  /**
   * Draws content on the GD image resource.
   */
  private function drawImageContent($gd_resource, array $content_lines, int $width, int $height): void {
    // Set background
    $bg_color = imagecolorallocate($gd_resource, 255, 255, 255);
    imagefill($gd_resource, 0, 0, $bg_color);

    // Add subtle background gradient
    $this->addBackgroundGradient($gd_resource, $width, $height);

    $y_position = 30;
    $line_spacing = 35;

    foreach ($content_lines as $line) {
      if (empty($line['text'])) {
        $y_position += $line_spacing / 2;
        continue;
      }

      // Parse color
      $color_rgb = $this->hexToRgb($line['color'] ?? '#000000');
      $text_color = imagecolorallocate($gd_resource, $color_rgb[0], $color_rgb[1], $color_rgb[2]);

      // Use built-in fonts for better compatibility
      $font_size = $this->mapFontSize($line['size'] ?? 14);
      
      // Add text shadow for better readability
      if ($line['bold'] ?? false) {
        $shadow_color = imagecolorallocate($gd_resource, 200, 200, 200);
        imagestring($gd_resource, $font_size, 21, $y_position + 1, $line['text'], $shadow_color);
      }

      imagestring($gd_resource, $font_size, 20, $y_position, $line['text'], $text_color);
      
      $y_position += $line_spacing;
    }

    // Add border
    $border_color = imagecolorallocate($gd_resource, 220, 220, 220);
    imagerectangle($gd_resource, 0, 0, $width - 1, $height - 1, $border_color);
  }

  /**
   * Adds a subtle background gradient.
   */
  private function addBackgroundGradient($gd_resource, int $width, int $height): void {
    for ($y = 0; $y < $height; $y++) {
      $alpha = min(20, $y / 10);
      $gradient_color = imagecolorallocate($gd_resource, 250 - $alpha, 250 - $alpha, 255);
      imageline($gd_resource, 0, $y, $width, $y, $gradient_color);
    }
  }

  /**
   * Converts hex color to RGB array.
   */
  private function hexToRgb(string $hex): array {
    $hex = ltrim($hex, '#');
    return [
      hexdec(substr($hex, 0, 2)),
      hexdec(substr($hex, 2, 2)),
      hexdec(substr($hex, 4, 2))
    ];
  }

  /**
   * Maps font size to GD built-in font constants.
   */
  private function mapFontSize(int $size): int {
    if ($size >= 20) return 5;
    if ($size >= 16) return 4;
    if ($size >= 14) return 3;
    if ($size >= 12) return 2;
    return 1;
  }
}
