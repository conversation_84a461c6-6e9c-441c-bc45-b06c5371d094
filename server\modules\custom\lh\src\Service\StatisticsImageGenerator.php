<?php

declare(strict_types=1);

namespace Drupal\lh\Service;

use Drupal\Core\File\FileSystemInterface;
use Drupal\Core\Image\ImageFactory;
use <PERSON>upal\Core\Logger\LoggerChannelInterface;

/**
 * Service for generating statistics images using Drupal Image API.
 */
class StatisticsImageGenerator {

  public function __construct(
    private readonly ImageFactory $imageFactory,
    private readonly FileSystemInterface $fileSystem,
    private readonly LoggerChannelInterface $logger,
  ) {}

  /**
   * Generates a statistics image from data array.
   *
   * @param array $statistics
   *   The statistics data array.
   * @param string $title
   *   The title for the image.
   *
   * @return string|null
   *   The file path of the generated image, or NULL on failure.
   */
  public function generateStatisticsImage(array $statistics, string $title = '📊 下注统计报告'): ?string {
    try {
      // Try HTML Canvas method first (best compatibility)
      $canvas_path = $this->generateCanvasImage($statistics, $title);
      if ($canvas_path) {
        return $canvas_path;
      }

      // Fallback to SVG
      $svg_path = $this->generateSvgImage($statistics, $title);
      if ($svg_path) {
        return $svg_path;
      }

      // Final fallback to text format
      return $this->generateTextFile($statistics, $title);

    } catch (\Exception $e) {
      $this->logger->error('Error generating statistics image: @message', ['@message' => $e->getMessage()]);
      return NULL;
    }
  }

  /**
   * Generates SVG image with perfect Chinese support.
   */
  private function generateSvgImage(array $statistics, string $title): ?string {
    try {
      $content_lines = $this->prepareContentLines($statistics, $title);
      $svg_content = $this->generateSvgContent($content_lines);

      // Save SVG file first
      $svg_filename = 'statistics_' . date('YmdHis') . '_' . uniqid() . '.svg';
      $directory = 'public://lh/statistics';
      $this->fileSystem->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY);
      $svg_file_path = $directory . '/' . $svg_filename;

      $svg_real_path = $this->fileSystem->realpath($svg_file_path);
      if (!file_put_contents($svg_real_path, $svg_content)) {
        return NULL;
      }

      $this->logger->info('SVG statistics generated: @path', ['@path' => $svg_file_path]);

      // Try to convert SVG to JPG/PNG
      $image_path = $this->convertSvgToImage($svg_real_path);
      if ($image_path) {
        // Delete SVG file after successful conversion
        unlink($svg_real_path);
        return $image_path;
      }

      // If conversion fails, return SVG path
      return $svg_real_path;

    } catch (\Exception $e) {
      $this->logger->warning('SVG generation failed: @message', ['@message' => $e->getMessage()]);
      return NULL;
    }
  }

  /**
   * Prepares content lines from statistics data.
   */
  private function prepareContentLines(array $statistics, string $title): array {
    $lines = [];
    $lines[] = ['text' => $title, 'size' => 24, 'color' => '#2c3e50', 'bold' => true];
    $lines[] = ['text' => str_repeat('─', 50), 'size' => 16, 'color' => '#bdc3c7'];

    // Overall statistics
    if (isset($statistics['overall'])) {
      $overall = $statistics['overall'];
      $lines[] = ['text' => '📈 总体统计', 'size' => 20, 'color' => '#e74c3c', 'bold' => true];
      $lines[] = ['text' => "总下注次数：{$overall['wager_count']}次", 'size' => 16, 'color' => '#34495e'];
      $lines[] = ['text' => "总金额：{$overall['total_amount']}元", 'size' => 16, 'color' => '#34495e'];
      $lines[] = ['text' => '', 'size' => 12]; // Empty line
    }

    // Regional statistics
    $regions = [
      'macau' => ['name' => '🇲🇴 澳门地区', 'color' => '#27ae60'],
      'hongkong' => ['name' => '🇭🇰 香港地区', 'color' => '#3498db']
    ];

    foreach ($regions as $region_key => $region_info) {
      if (isset($statistics[$region_key]) && $statistics[$region_key]['wager_count'] > 0) {
        $region_data = $statistics[$region_key];
        
        $lines[] = ['text' => $region_info['name'], 'size' => 18, 'color' => $region_info['color'], 'bold' => true];
        $lines[] = ['text' => "下注次数：{$region_data['wager_count']}次", 'size' => 14, 'color' => '#34495e'];
        $lines[] = ['text' => "金额：{$region_data['total_amount']}元", 'size' => 14, 'color' => '#34495e'];

        // Numbers statistics
        if (!empty($region_data['numbers'])) {
          $lines[] = ['text' => '🔢 号码统计：', 'size' => 16, 'color' => '#8e44ad', 'bold' => true];
          $number_chunks = array_chunk($region_data['numbers'], 6, true);
          foreach ($number_chunks as $chunk) {
            $number_line = '';
            foreach ($chunk as $number => $amount) {
              $number_line .= "  {$number}号：{$amount}元";
            }
            $lines[] = ['text' => $number_line, 'size' => 14, 'color' => '#34495e'];
          }
        }

        // Animals statistics
        if (!empty($region_data['animals'])) {
          $lines[] = ['text' => '🐾 生肖统计：', 'size' => 16, 'color' => '#f39c12', 'bold' => true];
          $animal_chunks = array_chunk($region_data['animals'], 4, true);
          foreach ($animal_chunks as $chunk) {
            $animal_line = '';
            foreach ($chunk as $animal => $amount) {
              $animal_line .= "  {$animal}（各数）：{$amount}元";
            }
            $lines[] = ['text' => $animal_line, 'size' => 14, 'color' => '#34495e'];
          }
        }

        $lines[] = ['text' => '', 'size' => 12]; // Empty line
      }
    }

    $lines[] = ['text' => '📊 统计完成 - ' . date('Y-m-d H:i:s'), 'size' => 14, 'color' => '#95a5a6'];

    return $lines;
  }

  /**
   * Draws content on the GD image resource.
   */
  private function drawImageContent($gd_resource, array $content_lines, int $width, int $height): void {
    // Set background
    $bg_color = imagecolorallocate($gd_resource, 255, 255, 255);
    imagefill($gd_resource, 0, 0, $bg_color);

    // Add subtle background gradient
    $this->addBackgroundGradient($gd_resource, $width, $height);

    $y_position = 40;
    $line_spacing = 35;

    // Get font path
    $font_path = $this->getFontPath();

    foreach ($content_lines as $line) {
      if (empty($line['text'])) {
        $y_position += (int)($line_spacing / 2);
        continue;
      }

      // Parse color
      $color_rgb = $this->hexToRgb($line['color'] ?? '#000000');
      $text_color = imagecolorallocate($gd_resource, (int) $color_rgb[0], (int) $color_rgb[1], (int) $color_rgb[2]);

      // Get font size
      $font_size = $line['size'] ?? 14;

      // Add text shadow for better readability
      if ($line['bold'] ?? false) {
        $shadow_color = imagecolorallocate($gd_resource, 200, 200, 200);
        if ($font_path) {
          imagettftext($gd_resource, $font_size, 0, 21, (int)($y_position + 1), $shadow_color, $font_path, $line['text']);
        } else {
          // Fallback to built-in font
          $builtin_size = $this->mapFontSize($font_size);
          imagestring($gd_resource, $builtin_size, 21, (int)($y_position + 1 - $font_size), $line['text'], $shadow_color);
        }
      }

      // Draw main text
      if ($font_path) {
        imagettftext($gd_resource, $font_size, 0, 20, (int)$y_position, $text_color, $font_path, $line['text']);
      } else {
        // Fallback to built-in font
        $builtin_size = $this->mapFontSize($font_size);
        imagestring($gd_resource, $builtin_size, 20, (int)($y_position - $font_size), $line['text'], $text_color);
      }

      $y_position += $line_spacing;
    }

    // Add border
    $border_color = imagecolorallocate($gd_resource, 220, 220, 220);
    imagerectangle($gd_resource, 0, 0, $width - 1, $height - 1, $border_color);
  }

  /**
   * Adds a subtle background gradient.
   */
  private function addBackgroundGradient($gd_resource, int $width, int $height): void {
    for ($y = 0; $y < $height; $y++) {
      $alpha = (int) min(20, $y / 10);
      $gradient_color = imagecolorallocate($gd_resource, (int)(250 - $alpha), (int)(250 - $alpha), 255);
      imageline($gd_resource, 0, $y, $width, $y, $gradient_color);
    }
  }

  /**
   * Converts hex color to RGB array.
   */
  private function hexToRgb(string $hex): array {
    $hex = ltrim($hex, '#');

    // Ensure we have a valid 6-character hex string
    if (strlen($hex) !== 6) {
      $hex = '000000'; // Default to black
    }

    return [
      (int) hexdec(substr($hex, 0, 2)),
      (int) hexdec(substr($hex, 2, 2)),
      (int) hexdec(substr($hex, 4, 2))
    ];
  }

  /**
   * Maps font size to GD built-in font constants.
   */
  private function mapFontSize(int $size): int {
    if ($size >= 20) return 5;
    if ($size >= 16) return 4;
    if ($size >= 14) return 3;
    if ($size >= 12) return 2;
    return 1;
  }

  /**
   * Gets the path to a Chinese-compatible TTF font.
   */
  private function getFontPath(): ?string {
    // Try to find system fonts that support Chinese
    $possible_fonts = [
      // Windows fonts
      'C:/Windows/Fonts/msyh.ttc',      // Microsoft YaHei
      'C:/Windows/Fonts/msyhbd.ttc',    // Microsoft YaHei Bold
      'C:/Windows/Fonts/simsun.ttc',    // SimSun
      'C:/Windows/Fonts/simhei.ttf',    // SimHei

      // Linux fonts
      '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
      '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
      '/System/Library/Fonts/PingFang.ttc', // macOS

      // Module fonts directory
      DRUPAL_ROOT . '/modules/custom/lh/fonts/NotoSansCJK-Regular.ttc',
      DRUPAL_ROOT . '/modules/custom/lh/fonts/SourceHanSans-Regular.ttf',
    ];

    foreach ($possible_fonts as $font_path) {
      if (file_exists($font_path) && is_readable($font_path)) {
        return $font_path;
      }
    }

    // Log warning if no font found
    $this->logger->warning('No Chinese-compatible TTF font found. Text may display as squares.');
    return null;
  }

  /**
   * Generates SVG content with Chinese text support.
   */
  private function generateSvgContent(array $content_lines): string {
    $width = 800;
    $height = max(600, count($content_lines) * 35 + 100);

    $svg = <<<SVG
<?xml version="1.0" encoding="UTF-8"?>
<svg width="{$width}" height="{$height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', 'SimHei', sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .section { font-family: 'Microsoft YaHei', 'SimHei', sans-serif; font-size: 18px; font-weight: bold; fill: #e74c3c; }
      .subsection { font-family: 'Microsoft YaHei', 'SimHei', sans-serif; font-size: 16px; font-weight: bold; fill: #8e44ad; }
      .text { font-family: 'Microsoft YaHei', 'SimHei', sans-serif; font-size: 14px; fill: #34495e; }
      .small { font-family: 'Microsoft YaHei', 'SimHei', sans-serif; font-size: 12px; fill: #95a5a6; }
    </style>
  </defs>

  <!-- Background -->
  <rect width="{$width}" height="{$height}" fill="#ffffff" stroke="#ddd" stroke-width="2"/>

  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="{$width}" height="{$height}" fill="url(#bgGradient)"/>

SVG;

    $y_position = 40;
    foreach ($content_lines as $line) {
      if (empty($line['text'])) {
        $y_position += 15;
        continue;
      }

      $text = htmlspecialchars($line['text'], ENT_XML1, 'UTF-8');
      $size = $line['size'] ?? 14;

      // Determine CSS class based on size
      $css_class = 'text';
      if ($size >= 24) $css_class = 'title';
      elseif ($size >= 18) $css_class = 'section';
      elseif ($size >= 16) $css_class = 'subsection';
      elseif ($size <= 12) $css_class = 'small';

      $svg .= "  <text x=\"30\" y=\"{$y_position}\" class=\"{$css_class}\">{$text}</text>\n";
      $y_position += 30;
    }

    $svg .= "</svg>";
    return $svg;
  }

  /**
   * Generates a simple text file as fallback.
   */
  private function generateTextFile(array $statistics, string $title): ?string {
    try {
      $content = $this->formatStatisticsText($statistics, $title);

      $filename = 'statistics_' . date('YmdHis') . '_' . uniqid() . '.txt';
      $directory = 'public://lh/statistics';
      $this->fileSystem->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY);
      $file_path = $directory . '/' . $filename;

      $real_path = $this->fileSystem->realpath($file_path);
      if (file_put_contents($real_path, $content)) {
        $this->logger->info('Text statistics generated: @path', ['@path' => $file_path]);
        return $real_path;
      }

      return NULL;
    } catch (\Exception $e) {
      $this->logger->error('Text generation failed: @message', ['@message' => $e->getMessage()]);
      return NULL;
    }
  }

  /**
   * Formats statistics as plain text.
   */
  private function formatStatisticsText(array $statistics, string $title): string {
    $lines = [$title, str_repeat('=', 50), ''];

    // Overall statistics
    if (isset($statistics['overall'])) {
      $overall = $statistics['overall'];
      $lines[] = '📈 总体统计';
      $lines[] = "总下注次数：{$overall['wager_count']}次";
      $lines[] = "总金额：{$overall['total_amount']}元";
      $lines[] = '';
    }

    // Regional statistics
    $regions = [
      'macau' => '🇲🇴 澳门地区',
      'hongkong' => '🇭🇰 香港地区'
    ];

    foreach ($regions as $region_key => $region_name) {
      if (isset($statistics[$region_key]) && $statistics[$region_key]['wager_count'] > 0) {
        $region_data = $statistics[$region_key];

        $lines[] = $region_name;
        $lines[] = "下注次数：{$region_data['wager_count']}次";
        $lines[] = "金额：{$region_data['total_amount']}元";

        if (!empty($region_data['numbers'])) {
          $lines[] = '🔢 号码统计：';
          foreach ($region_data['numbers'] as $number => $amount) {
            $lines[] = "  {$number}号：{$amount}元";
          }
        }

        if (!empty($region_data['animals'])) {
          $lines[] = '🐾 生肖统计：';
          foreach ($region_data['animals'] as $animal => $amount) {
            $lines[] = "  {$animal}（各数）：{$amount}元";
          }
        }

        $lines[] = '';
      }
    }

    $lines[] = '📊 统计完成 - ' . date('Y-m-d H:i:s');

    return implode("\n", $lines);
  }

  /**
   * Generates image using HTML Canvas (best Chinese support).
   */
  private function generateCanvasImage(array $statistics, string $title): ?string {
    try {
      $content_lines = $this->prepareContentLines($statistics, $title);
      $html_content = $this->generateCanvasHtml($content_lines, $title);

      // Save HTML file
      $html_filename = 'canvas_' . date('YmdHis') . '_' . uniqid() . '.html';
      $directory = 'public://lh/statistics';
      $this->fileSystem->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY);
      $html_file_path = $directory . '/' . $html_filename;

      $html_real_path = $this->fileSystem->realpath($html_file_path);
      if (file_put_contents($html_real_path, $html_content)) {
        $this->logger->info('Canvas HTML generated: @path', ['@path' => $html_file_path]);
        return $html_real_path;
      }

      return NULL;
    } catch (\Exception $e) {
      $this->logger->warning('Canvas generation failed: @message', ['@message' => $e->getMessage()]);
      return NULL;
    }
  }

  /**
   * Generates HTML with Canvas for image generation.
   */
  private function generateCanvasHtml(array $content_lines, string $title): string {
    $stats_text = $this->formatCanvasText($content_lines);

    return <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Statistics Canvas</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'SimHei', 'Arial Unicode MS', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        #canvas {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .info {
            margin-top: 20px;
            padding: 10px;
            background: #e8f4fd;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <canvas id="canvas" width="800" height="600"></canvas>
    <div class="info">
        <strong>使用说明：</strong><br>
        1. 右键点击图片 → 另存为图片<br>
        2. 或者截图保存此区域<br>
        3. 生成时间：{$this->getCurrentTime()}
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');

        // 设置高质量渲染
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        // 背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, 800, 600);

        // 渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 0, 600);
        gradient.addColorStop(0, '#f8f9fa');
        gradient.addColorStop(1, '#ffffff');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 800, 600);

        // 绘制内容
        {$stats_text}

        // 边框
        ctx.strokeStyle = '#ddd';
        ctx.lineWidth = 2;
        ctx.strokeRect(1, 1, 798, 598);

        // 添加水印
        ctx.fillStyle = '#f0f0f0';
        ctx.font = '12px Microsoft YaHei';
        ctx.fillText('LH统计系统 - {$this->getCurrentTime()}', 600, 580);
    </script>
</body>
</html>
HTML;
  }

  /**
   * Formats content for Canvas drawing.
   */
  private function formatCanvasText(array $content_lines): string {
    $js_lines = [];
    $y = 50;

    foreach ($content_lines as $line) {
      if (empty($line['text'])) {
        $y += 15;
        continue;
      }

      $text = addslashes($line['text']);
      $size = $line['size'] ?? 14;
      $color = $line['color'] ?? '#34495e';
      $bold = $line['bold'] ?? false;

      $font_weight = $bold ? 'bold ' : '';
      $font = "'{$font_weight}{$size}px Microsoft YaHei, SimHei, sans-serif'";

      $js_lines[] = "ctx.fillStyle = '{$color}';";
      $js_lines[] = "ctx.font = {$font};";
      $js_lines[] = "ctx.fillText('{$text}', 30, {$y});";

      $y += ($size + 10);
    }

    return implode("\n        ", $js_lines);
  }

  /**
   * Gets current time string.
   */
  private function getCurrentTime(): string {
    return date('Y-m-d H:i:s');
  }

  /**
   * Converts SVG to image using multiple methods.
   */
  private function convertSvgToImage(string $svg_path): ?string {
    $base_name = pathinfo($svg_path, PATHINFO_FILENAME);
    $directory = dirname($svg_path);
    $jpg_path = $directory . DIRECTORY_SEPARATOR . $base_name . '.jpg';

    // Try different conversion methods
    $methods = [
      'imagick' => function() use ($svg_path, $jpg_path) {
        return $this->convertWithImagick($svg_path, $jpg_path);
      },
      'inkscape' => function() use ($svg_path, $jpg_path) {
        return $this->convertWithInkscape($svg_path, $jpg_path);
      },
      'rsvg' => function() use ($svg_path, $jpg_path) {
        return $this->convertWithRsvg($svg_path, $jpg_path);
      },
      'cairosvg' => function() use ($svg_path, $jpg_path) {
        return $this->convertWithCairoSvg($svg_path, $jpg_path);
      }
    ];

    foreach ($methods as $method_name => $method) {
      try {
        if ($method() && file_exists($jpg_path)) {
          $this->logger->info('SVG converted to JPG using @method: @path', [
            '@method' => $method_name,
            '@path' => $jpg_path
          ]);
          return $jpg_path;
        }
      } catch (\Exception $e) {
        $this->logger->debug('SVG conversion method @method failed: @message', [
          '@method' => $method_name,
          '@message' => $e->getMessage()
        ]);
      }
    }

    $this->logger->warning('All SVG conversion methods failed, keeping SVG format');
    return NULL;
  }

  /**
   * Convert SVG using Imagick extension.
   */
  private function convertWithImagick(string $svg_path, string $jpg_path): bool {
    if (!extension_loaded('imagick')) {
      return false;
    }

    try {
      $imagick = new \Imagick();
      $imagick->setBackgroundColor(new \ImagickPixel('white'));
      $imagick->readImage($svg_path);
      $imagick->setImageFormat('jpeg');
      $imagick->setImageCompressionQuality(90);
      $imagick->writeImage($jpg_path);
      $imagick->clear();
      $imagick->destroy();
      return true;
    } catch (\Exception $e) {
      return false;
    }
  }

  /**
   * Convert SVG using Inkscape command line.
   */
  private function convertWithInkscape(string $svg_path, string $jpg_path): bool {
    $command = "inkscape --export-type=jpeg --export-filename=\"{$jpg_path}\" \"{$svg_path}\" 2>nul";
    exec($command, $output, $return_code);
    return $return_code === 0;
  }

  /**
   * Convert SVG using rsvg-convert.
   */
  private function convertWithRsvg(string $svg_path, string $jpg_path): bool {
    $command = "rsvg-convert -f jpeg -o \"{$jpg_path}\" \"{$svg_path}\" 2>nul";
    exec($command, $output, $return_code);
    return $return_code === 0;
  }

  /**
   * Convert SVG using CairoSVG (Python).
   */
  private function convertWithCairoSvg(string $svg_path, string $jpg_path): bool {
    $command = "python -m cairosvg \"{$svg_path}\" -o \"{$jpg_path}\" 2>nul";
    exec($command, $output, $return_code);
    return $return_code === 0;
  }
}
