<?php

declare(strict_types=1);

namespace Drupal\lh\Service;

use Drupal\Core\Logger\LoggerChannelInterface;
use GuzzleHttp\ClientInterface;

/**
 * WeChat API client for communicating with WeChat framework.
 */
class WechatApiClient {

  private string $baseUrl = 'http://127.0.0.1:8888/wechat/httpapi';

  public function __construct(
    private readonly ClientInterface $httpClient,
    private readonly LoggerChannelInterface $logger,
  ) {}

  /**
   * Sets the base URL for API requests.
   */
  public function setBaseUrl(string $url): void {
    $this->baseUrl = $url;
  }

  /**
   * Sends API request to WeChat framework.
   */
  private function sendRequest(string $type, array $data = []): ?array {
    $payload = ['type' => $type, 'data' => $data];
    $response = $this->makeHttpRequest($payload);

    if (!$response) {
      return NULL;
    }

    $result = json_decode($response, TRUE);
    return ($result && isset($result['code']) && $result['code'] === 200) ? $result : NULL;
  }

  /**
   * Makes HTTP request to WeChat framework.
   */
  private function makeHttpRequest(array $payload): ?string {
    $response = $this->httpClient->post($this->baseUrl, [
      'json' => $payload,
      'timeout' => 10,
      'http_errors' => FALSE,
    ]);

    if (!$response) {
      $this->logger->error('WeChat API request failed: No response received');
      return NULL;
    }

    if ($response->getStatusCode() !== 200) {
      $this->logger->error('WeChat API request failed: HTTP @status', ['@status' => $response->getStatusCode()]);
      return NULL;
    }

    $body = $response->getBody();
    if (!$body) {
      $this->logger->error('WeChat API request failed: Empty response body');
      return NULL;
    }

    return $body->getContents();
  }

  /**
   * Gets current user's personal information.
   */
  public function getSelfInfo(): ?array {
    return $this->sendRequest('getSelfInfo');
  }

  /**
   * Sends text message to specified contact.
   */
  public function sendText(string $wxid, string $msg): ?array {
    return $this->sendRequest('sendText', ['wxid' => $wxid, 'msg' => $msg]);
  }

  /**
   * Sends image to specified contact.
   */
  public function sendImage(string $wxid, string $image_path): ?array {
    return $this->sendRequest('sendImage', ['wxid' => $wxid, 'imagePath' => $image_path]);
  }

}
