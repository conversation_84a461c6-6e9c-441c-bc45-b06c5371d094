<?php

declare(strict_types=1);

namespace Drupal\lh\Service;

use Drupal\Core\Logger\LoggerChannelFactoryInterface;
use Drupal\Core\Logger\LoggerChannelInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;

/**
 * WeChat event handler service for processing various WeChat events.
 */
class WechatEventHandler {

  private readonly LoggerChannelInterface $logger;
  private ?array $currentWechatInfo = NULL;

  public function __construct(
    LoggerChannelFactoryInterface $logger_factory,
    private readonly WechatApiClient $apiClient,
    private readonly EntityTypeManagerInterface $entityTypeManager,
    private readonly StatisticsImageGenerator $imageGenerator,
  ) {
    $this->logger = $logger_factory->get('wechat_handler');
  }

  /**
   * Gets current WeChat account information.
   */
  private function getCurrentWechatInfo(): ?array {
    if ($this->currentWechatInfo === NULL) {
      $this->currentWechatInfo = $this->apiClient->getSelfInfo();
    }
    return $this->currentWechatInfo;
  }

  /**
   * Gets all trigger keywords configuration.
   *
   * @return array All trigger keywords organized by category
   */
  private function getTriggerKeywords(): array {
    return [
      // 管理员设置相关
      'admin_set' => ['设置管理员'],
      'admin_remove' => ['移除管理员'],

      // 游戏控制相关
      'game_start' => ['开始', '开机', '开局', '开搞', '开工'],
      'game_end' => ['收工', '结束', '下班'],

      // 统计功能
      'statistics' => ['统计'],

      // 下注相关（非管理员功能）
      'wager_place' => [], // 下注通过正则匹配，不需要关键词

      // 未来可扩展的其他功能
      // 'feature_xxx' => ['关键词1', '关键词2'],
      // 'feature_yyy' => ['关键词3', '关键词4'],
    ];
  }

  /**
   * Checks if message exactly matches any keywords from specified category.
   *
   * @param string $message The message to check
   * @param string $category The keyword category to check against
   * @return bool TRUE if message exactly matches any keyword from the category
   */
  private function containsKeywords(string $message, string $category): bool {
    $keywords = $this->getTriggerKeywords();

    if (!isset($keywords[$category])) {
      return FALSE;
    }

    $clean_message = trim($message);

    foreach ($keywords[$category] as $keyword) {
      if ($clean_message === $keyword) {
        return TRUE;
      }
    }

    return FALSE;
  }

  /**
   * Checks if message is an admin command, supporting @ mentions.
   *
   * @param string $message The message to check
   * @param string $category The admin command category
   * @return bool TRUE if message matches admin command pattern
   */
  private function isAdminCommand(string $message, string $category): bool {
    $keywords = $this->getTriggerKeywords();

    if (!isset($keywords[$category])) {
      return FALSE;
    }

    $clean_message = trim($message);

    foreach ($keywords[$category] as $keyword) {
      // 完全匹配
      if ($clean_message === $keyword) {
        return TRUE;
      }

      // 移除@部分后再匹配
      // 匹配并移除所有@用户名部分，然后检查剩余部分是否为关键词
      $message_without_at = preg_replace('/@\S+/u', '', $clean_message);
      $message_without_at = preg_replace('/\s+/', ' ', trim($message_without_at));

      if ($message_without_at === $keyword) {
        return TRUE;
      }

      // 带@的格式匹配：@用户名 关键词 或 @用户名 @用户名 关键词
      if (preg_match('/^(@\S+\s+)+' . preg_quote($keyword, '/') . '$/u', $clean_message)) {
        return TRUE;
      }
    }

    return FALSE;
  }

  /**
   * Gets all trigger keywords as a flat array for debugging.
   *
   * @return array All keywords with their categories
   */
  private function getAllTriggerKeywords(): array {
    $keywords = $this->getTriggerKeywords();
    $result = [];

    foreach ($keywords as $category => $keyword_list) {
      foreach ($keyword_list as $keyword) {
        $result[] = [
          'keyword' => $keyword,
          'category' => $category,
        ];
      }
    }

    return $result;
  }



  private function isWagerCommand(string $message): bool {
    $clean_message = preg_replace('/澳门|香港|🇲🇴|🇭🇰/u', '', $message);
    $clean_message = preg_replace('/\s+/', ' ', trim($clean_message));

    if (!preg_match('/\d/', $clean_message)) {
      return FALSE;
    }

    $has_numbers = (bool) preg_match('/\b(?:0[1-9]|[1-4][0-9])\b/', $clean_message);
    $has_animals = (bool) preg_match('/[羊牛虎猴鼠兔马蛇龙狗鸡猪]/u', $clean_message);

    if (!$has_numbers && !$has_animals) {
      return FALSE;
    }

    return (bool) preg_match('/各(?:数)?\s*\d+/', $clean_message);
  }

  private function parseWagerCommand(string $message, string $wxid_room = '', string $wxid = ''): array {
    $region = 'macau'; // 默认澳门
    if (preg_match('/香港|��/u', $message)) {
      $region = 'hongkong';
    } elseif (preg_match('/澳门|��/u', $message)) {
      $region = 'macau';
    }

    $result = [
      'region' => $region,
      'wxid_room' => $wxid_room,
      'wxid' => $wxid,
      'bets' => [],
    ];

    $clean_message = preg_replace('/澳门|香港|🇲🇴|🇭🇰/u', '', $message);
    $lines = preg_split('/[\r\n]+/', trim($clean_message));
    $bet_lines = [];
    $amount_info = NULL;

    foreach ($lines as $line) {
      $line = trim($line);
      if (empty($line)) continue;

      if (preg_match('/(.+?)各(?:数)?\s*(\d+)([米元])?/u', $line, $matches)) {
        $items_text = $matches[1];
        $items_text = preg_replace('/[\x{2014}\x{2013}\x{002D}—-]+\.?$/u', '', $items_text);
        $items_text = preg_replace('/\.+$/', '', $items_text);
        $amount = (int)$matches[2];
        $this->processBetItems($items_text, $amount, $result);
        continue;
      }

      if (preg_match('/^([羊牛虎猴鼠兔马蛇龙狗鸡猪]|\d{1,2})\s*[-—]?\s*(\d+)([米元])?$/u', $line, $matches)) {
        $item = $matches[1];
        $amount = (int)$matches[2];
        $this->processBetItems($item, $amount, $result);
        continue;
      }

      if (preg_match('/([羊牛虎猴鼠兔马蛇龙狗鸡猪]+).*?(\d+)([米元])?/u', $line, $matches)) {
        $animals_text = $matches[1];
        $amount = (int)$matches[2];
        $this->processBetItems($animals_text, $amount, $result);
        continue;
      }

      if (preg_match('/^\s*各(?:数)?.*?(\d+)([米元])?$/u', $line, $matches)) {
        $amount_info = [
          'amount' => (int)$matches[1],
          'unit' => $matches[2] ?? '',
        ];
        continue;
      }

      // 只有包含数字但不包含生肖文字的行才被认为是数字行
      if (preg_match('/\d/', $line) && !preg_match('/[羊牛虎猴鼠兔马蛇龙狗鸡猪]/', $line)) {
        $bet_lines[] = $line;
      }
    }

    if (!empty($bet_lines) && $amount_info) {
      foreach ($bet_lines as $bet_line) {
        $this->processBetItems($bet_line, $amount_info['amount'], $result);
      }
    }

    return $result;
  }

  private function processBetItems(string $items_text, int $amount, array &$result): void {
    // 处理直接输入的数字
    $numbers = $this->extractNumbers($items_text);
    foreach ($numbers as $number) {
      $result['bets'][] = [
        'type' => 'number',
        'value' => $number,
        'amount' => $amount,
        'source' => 'direct',
      ];
    }

    // 处理生肖：直接作为生肖下注，不展开成数字
    $animals = $this->extractAnimals($items_text);
    foreach ($animals as $animal) {
      $result['bets'][] = [
        'type' => 'animal',
        'value' => $animal,
        'amount' => $amount,
        'source' => 'direct',
      ];
    }
  }

  private function extractNumbers(string $text): array {
    // 匹配1-49的数字，支持各种分隔符
    preg_match_all('/\b([0-4]?[0-9])\b/', $text, $matches);
    $numbers = array_unique($matches[1]);
    $formatted_numbers = [];
    foreach ($numbers as $number) {
      $num = (int)$number;
      if ($num >= 1 && $num <= 49) {
        $formatted_numbers[] = str_pad((string)$num, 2, '0', STR_PAD_LEFT);
      }
    }
    return array_unique($formatted_numbers);
  }

  private function extractAnimals(string $text): array {
    $animals = ['羊', '牛', '虎', '猴', '鼠', '兔', '马', '蛇', '龙', '狗', '鸡', '猪'];
    $found = [];

    // 只匹配直接输入的生肖文字，不根据数字反推
    foreach ($animals as $animal) {
      if (strpos($text, $animal) !== FALSE) {
        $found[] = $animal;
      }
    }

    return $found;
  }

  /**
   * Gets the zodiac animal for a given number based on 2025 Snake Year mapping.
   *
   * @param string $number Two-digit number (01-49)
   * @return string|null The corresponding zodiac animal or null if not found
   */
  private function getAnimalByNumber(string $number): ?string {
    // 2025年蛇年六合彩生肖数字对照表
    $zodiac_map = [
      // 蛇（本命年，5个数字）
      '01' => '蛇', '13' => '蛇', '25' => '蛇', '37' => '蛇', '49' => '蛇',

      // 其他生肖（各4个数字）
      '06' => '鼠', '18' => '鼠', '30' => '鼠', '42' => '鼠',
      '05' => '牛', '17' => '牛', '29' => '牛', '41' => '牛',
      '04' => '虎', '16' => '虎', '28' => '虎', '40' => '虎',
      '03' => '兔', '15' => '兔', '27' => '兔', '39' => '兔',
      '02' => '龙', '14' => '龙', '26' => '龙', '38' => '龙',
      '12' => '马', '24' => '马', '36' => '马', '48' => '马',
      '11' => '羊', '23' => '羊', '35' => '羊', '47' => '羊',
      '10' => '猴', '22' => '猴', '34' => '猴', '46' => '猴',
      '09' => '鸡', '21' => '鸡', '33' => '鸡', '45' => '鸡',
      '08' => '狗', '20' => '狗', '32' => '狗', '44' => '狗',
      '07' => '猪', '19' => '猪', '31' => '猪', '43' => '猪',
    ];

    return $zodiac_map[$number] ?? null;
  }

  /**
   * Gets all numbers for a given zodiac animal based on 2025 Snake Year mapping.
   *
   * @param string $animal The zodiac animal name
   * @return array Array of two-digit numbers for this animal
   */
  private function getNumbersByAnimal(string $animal): array {
    // 2025年蛇年六合彩生肖对应数字表
    $animal_numbers = [
      '蛇' => ['01', '13', '25', '37', '49'], // 本命年5个数字
      '鼠' => ['06', '18', '30', '42'],
      '牛' => ['05', '17', '29', '41'],
      '虎' => ['04', '16', '28', '40'],
      '兔' => ['03', '15', '27', '39'],
      '龙' => ['02', '14', '26', '38'],
      '马' => ['12', '24', '36', '48'],
      '羊' => ['11', '23', '35', '47'],
      '猴' => ['10', '22', '34', '46'],
      '鸡' => ['09', '21', '33', '45'],
      '狗' => ['08', '20', '32', '44'],
      '猪' => ['07', '19', '31', '43'],
    ];

    return $animal_numbers[$animal] ?? [];
  }

  private function processStatisticsCommand(array $data): array {
    $message_data = $data['data'] ?? [];
    $port = $data['port'] ?? 6999;

    if (($message_data['fromType'] ?? 0) !== 2) {
      return ['status' => 'ignored', 'reason' => 'Not a group message'];
    }

    $message = $message_data['msg'] ?? '';
    $wxid_room = $message_data['fromWxid'] ?? '';
    $wxid = $message_data['finalFromWxid'] ?? '';

    if (!$this->containsKeywords($message, 'statistics')) {
      return ['status' => 'ignored', 'reason' => 'Not a statistics command'];
    }

    if (!$this->isManager($wxid_room, $wxid)) {
      return ['status' => 'ignored', 'reason' => 'Not a manager'];
    }

    $statistics = $this->generateStatistics($wxid_room);

    // Generate statistics image instead of text
    $image_path = $this->imageGenerator->generateStatisticsImage($statistics);

    if ($image_path) {
      // Send image to group
      $this->sendGroupImage($wxid_room, $image_path, $port);
      $result_message = 'Statistics image generated and sent';
    } else {
      // Fallback to text if image generation fails
      $response = $this->formatStatisticsResponse($statistics);
      $this->sendGroupMessage($wxid_room, $response, $port);
      $result_message = 'Statistics generated as text (image generation failed)';
    }

    return [
      'status' => 'success',
      'message' => $result_message,
      'statistics' => $statistics,
      'image_path' => $image_path,
    ];
  }

  /**
   * Processes wager commands from users.
   */
  private function processWagerCommand(array $data): array {
    $message_data = $data['data'] ?? [];
    $port = $data['port'] ?? 6999;

    // Check if message is from group
    if (($message_data['fromType'] ?? 0) !== 2) {
      return ['status' => 'ignored', 'reason' => 'Not a group message'];
    }

    $message = $message_data['msg'] ?? '';
    $wxid_room = $message_data['fromWxid'] ?? '';
    $wxid = $message_data['finalFromWxid'] ?? '';

    // Check if it's a wager command first
    if (!$this->isWagerCommand($message)) {
      return ['status' => 'ignored', 'reason' => 'Not a wager command'];
    }

    // Check if game is started
    if (!$this->isGameStarted($wxid_room)) {
      $response = '游戏还没开始呢，等管理员开始游戏吧';
      $this->sendGroupMessage($wxid_room, $response, $port);
      return ['status' => 'ignored', 'reason' => 'Game not started'];
    }

    // 全员都可以下注，包括管理员
    // 注释掉管理员限制，允许所有人下注

    $wager_data = $this->parseWagerCommand($message, $wxid_room, $wxid);

    if (empty($wager_data['bets'])) {
      $response = '下注格式不正确';
      $this->sendGroupMessage($wxid_room, $response, $port);
      return ['status' => 'error', 'reason' => 'Invalid wager format'];
    }

    // Save wager to database
    $save_result = $this->saveWager($wxid_room, $wxid, $message, $wager_data);

    if ($save_result['success']) {
      $response = $this->generateWagerResponse($wager_data, $message);
      $this->sendGroupMessage($wxid_room, $response, $port);
      return [
        'status' => 'success',
        'message' => 'Wager placed successfully',
        'wager_data' => $wager_data,
        'save_result' => $save_result,
      ];
    }

    $response = '下注失败了，请稍后再试';
    $this->sendGroupMessage($wxid_room, $response, $port);
    return [
      'status' => 'error',
      'reason' => 'Failed to save wager',
      'save_result' => $save_result,
    ];
  }

  private function isGameStarted(string $wxid_room): bool {
    $storage = $this->entityTypeManager->getStorage('room');
    $rooms = $storage->loadByProperties(['title' => $wxid_room]);

    if (empty($rooms)) {
      return FALSE;
    }

    $room = reset($rooms);
    $game_status = $room->get('status_game')->value ?? 0;
    return $game_status == 1;
  }

  /**
   * Generates wager response message showing bet details.
   */
  private function generateWagerResponse(array $wager_data, string $original_message = ''): string {
    $region_text = $wager_data['region'] == 'macau' ? '澳门' : '香港';

    // 计算统计信息
    $total_amount = 0;
    $numbers = [];
    $animals = [];
    foreach ($wager_data['bets'] as $bet) {
      if ($bet['type'] === 'number') {
        $total_amount += $bet['amount'];
        $numbers[] = $bet['value'];
      } elseif ($bet['type'] === 'animal') {
        // 生肖下注：金额需要乘以对应数字数量
        $animal_numbers = $this->getNumbersByAnimal($bet['value']);
        $actual_amount = $bet['amount'] * count($animal_numbers);
        $total_amount += $actual_amount;
        $animals[] = $bet['value'];
      }
    }
    $response = "下注成功\r";
    $response .= "地区：{$region_text}\r";
    $response .= "总金额：{$total_amount}元";
    if (!empty($numbers)) {
      $numbers_text = implode(',', array_unique($numbers));
      $response .= "\r号码：{$numbers_text}";
    }
    if (!empty($animals)) {
      $animals_text = implode(',', $animals);
      $response .= "\r生肖：{$animals_text}";
    }

    if (!empty($original_message)) {
      $response .= "\r下注输入：\"{$original_message}\"";
    }

    return $response;
  }

  private function generateWagerName(array $wager_data, string $original_message): string {
    $region_text = $wager_data['region'] == 'macau' ? '澳门' : '香港';

    // 计算统计信息
    $total_amount = 0;
    $numbers = [];
    $animals = [];

    foreach ($wager_data['bets'] as $bet) {
      $total_amount += $bet['amount'];

      if ($bet['source'] === 'direct') {
        $numbers[] = $bet['value'];
      } elseif ($bet['source'] === 'animal') {
        $animal = $this->getAnimalByNumber($bet['value']);
        if ($animal && !in_array($animal, $animals)) {
          $animals[] = $animal;
        }
      }
    }

    $name_parts = [];
    $name_parts[] = $region_text;
    $name_parts[] = count($wager_data['bets']) . '注';
    $name_parts[] = $total_amount . '元';

    if (!empty($numbers)) {
      $numbers_count = count(array_unique($numbers));
      $name_parts[] = "号码{$numbers_count}个";
    }

    if (!empty($animals)) {
      $animals_count = count($animals);
      $name_parts[] = "生肖{$animals_count}个";
    }

    return implode(' ', $name_parts);
  }

  private function generateStatistics(string $wxid_room): array {
    $storage = $this->entityTypeManager->getStorage('wager');
    if (!$storage) {
      return ['error' => '无法获取存储服务'];
    }

    $wagers = $storage->loadByProperties(['wxid_room' => $wxid_room]);
    if (empty($wagers)) {
      return ['error' => '暂无下注数据'];
    }

    // 只统计进行中的轮次（status_round = 1）
    $active_wagers = [];
    foreach ($wagers as $wager) {
      $status_round = $wager->get('status_round')->value ?? 0;

      // 使用宽松比较，支持字符串"1"和整数1
      if ($status_round == 1) {
        $active_wagers[] = $wager;
      }
    }

    if (empty($active_wagers)) {
      return ['error' => '暂无进行中的下注数据'];
    }

    $statistics = [
      'hongkong' => [
        'numbers' => [],
        'animals' => [],
        'total_amount' => 0,
        'total_bets' => 0,
        'wager_count' => 0,
      ],
      'macau' => [
        'numbers' => [],
        'animals' => [],
        'total_amount' => 0,
        'total_bets' => 0,
        'wager_count' => 0,
      ],
      'overall' => [
        'total_amount' => 0,
        'total_bets' => 0,
        'wager_count' => count($active_wagers),
      ],
    ];

    foreach ($active_wagers as $wager) {
      $wager_json = $wager->get('wager')->value;
      if (!$wager_json) continue;

      $wager_data = json_decode($wager_json, TRUE);
      if (!$wager_data || !isset($wager_data['bets'])) continue;

      // 获取地区信息
      $region = $wager_data['region'] ?? 'macau';
      if (!isset($statistics[$region])) {
        $region = 'macau'; // 默认为澳门
      }

      $statistics[$region]['wager_count']++;

      foreach ($wager_data['bets'] as $bet) {
        $type = $bet['type'] ?? '';
        $value = $bet['value'] ?? '';
        $amount = $bet['amount'] ?? 0;

        if ($type === 'number') {
          // 直接数字下注显示在号码统计中
          if (!isset($statistics[$region]['numbers'][$value])) {
            $statistics[$region]['numbers'][$value] = 0;
          }
          $statistics[$region]['numbers'][$value] += $amount;

          // 数字下注使用原始金额计算总额
          $statistics[$region]['total_amount'] += $amount;
          $statistics['overall']['total_amount'] += $amount;
        } elseif ($type === 'animal') {
          // 生肖下注显示在生肖统计中，金额需要乘以该生肖对应的数字数量
          $animal_numbers = $this->getNumbersByAnimal($value);
          $actual_amount = $amount * count($animal_numbers);

          if (!isset($statistics[$region]['animals'][$value])) {
            $statistics[$region]['animals'][$value] = 0;
          }
          $statistics[$region]['animals'][$value] += $actual_amount;

          // 生肖下注使用计算后的金额计算总额
          $statistics[$region]['total_amount'] += $actual_amount;
          $statistics['overall']['total_amount'] += $actual_amount;
        }

        $statistics[$region]['total_bets']++;
        $statistics['overall']['total_bets']++;
      }
    }

    // 排序各地区的数据
    ksort($statistics['hongkong']['numbers']);
    ksort($statistics['hongkong']['animals']);
    ksort($statistics['macau']['numbers']);
    ksort($statistics['macau']['animals']);

    return $statistics;
  }

  private function formatStatisticsResponse(array $statistics): string {
    if (isset($statistics['error'])) {
      return $statistics['error'];
    }

    $response = "📊 下注统计报告\r";
    $response .= "总下注次数：{$statistics['overall']['wager_count']}次\r";
    $response .= "总金额：{$statistics['overall']['total_amount']}元\r";

    // 香港地区统计
    if ($statistics['hongkong']['wager_count'] > 0) {
      $response .= "🇭🇰 香港地区：\r";
      $response .= "下注次数：{$statistics['hongkong']['wager_count']}次\r";
      $response .= "金额：{$statistics['hongkong']['total_amount']}元\r";
      if (!empty($statistics['hongkong']['numbers'])) {
        $response .= "🔢 号码统计：\r";
        foreach ($statistics['hongkong']['numbers'] as $number => $amount) {
          $response .= "  {$number}号：{$amount}元\r";
        }
      }
      if (!empty($statistics['hongkong']['animals'])) {
        $response .= "🐾 生肖统计：\r";
        foreach ($statistics['hongkong']['animals'] as $animal => $amount) {
          if (is_string($animal) && !empty($animal) && $animal !== '0') {
            // 计算显示金额（实际金额除以数字数量）
            $animal_numbers = $this->getNumbersByAnimal($animal);
            $display_amount = count($animal_numbers) > 0 ? intval($amount / count($animal_numbers)) : $amount;
            $response .= "  {$animal}（各数）：{$display_amount}元\r";
          }
        }
      }
    }

    // 澳门地区统计
    if ($statistics['macau']['wager_count'] > 0) {
      $response .= "🇲🇴 澳门地区：\r";
      $response .= "下注次数：{$statistics['macau']['wager_count']}次\r";
      $response .= "金额：{$statistics['macau']['total_amount']}元\r";
      if (!empty($statistics['macau']['numbers'])) {
        $response .= "🔢 号码统计：\r";
        foreach ($statistics['macau']['numbers'] as $number => $amount) {
          $response .= "  {$number}号：{$amount}元\r";
        }
      }
      if (!empty($statistics['macau']['animals'])) {
        $response .= "🐾 生肖统计：\r";
        foreach ($statistics['macau']['animals'] as $animal => $amount) {
          if (is_string($animal) && !empty($animal) && $animal !== '0') {
            // 计算显示金额（实际金额除以数字数量）
            $animal_numbers = $this->getNumbersByAnimal($animal);
            $display_amount = count($animal_numbers) > 0 ? intval($amount / count($animal_numbers)) : $amount;
            $response .= "  {$animal}（各数）：{$display_amount}元\r";
          }
        }
      }
    }

    $response .= "📈 统计完成";
    return $response;
  }



  private function saveWager(string $wxid_room, string $wxid, string $original_message, array $wager_data): array {
    $storage = $this->entityTypeManager->getStorage('wager');
    if (!$storage) {
      return [
        'success' => FALSE,
        'message' => '下注保存失败：无法获取存储服务',
      ];
    }

    $wager_name = $this->generateWagerName($wager_data, $original_message);

    $wager = $storage->create([
      'title' => $wager_name,
      'wxid_room' => $wxid_room,
      'wxid' => $wxid,
      'uid' => 1,
      'status' => 1,
      'status_round' => 1, // 1表示进行中，2表示已完成
      'msg' => $original_message,
      'wager' => json_encode($wager_data, JSON_UNESCAPED_UNICODE),
      'created' => time(),
    ]);

    if (!$wager) {
      return [
        'success' => FALSE,
        'message' => '下注保存失败：无法创建实体',
      ];
    }

    if ($wager->save()) {
      return [
        'success' => TRUE,
        'message' => '下注保存成功',
        'wager_id' => $wager->id(),
      ];
    }

    return [
      'success' => FALSE,
      'message' => '下注保存失败：实体保存失败',
    ];
  }

  /**
   * Checks if message is sent by current WeChat account.
   */
  private function isMessageFromSelf(array $message_data): bool {
    $info = $this->getCurrentWechatInfo();
    $current_wxid = $info['result']['wxid'] ?? NULL;
    $final_from_wxid = $message_data['finalFromWxid'] ?? '';

    return $current_wxid && $final_from_wxid === $current_wxid;
  }

  /**
   * Sends message to WeChat group.
   *
   * @param string $wxid_room Group WeChat ID
   * @param string $message Message content
   * @param int $port API port
   * @param bool $is_auto_reply Whether to add "哎哟，" prefix (default: TRUE to prevent loops)
   */
  private function sendGroupMessage(string $wxid_room, string $message, int $port = 6999, bool $is_auto_reply = TRUE): array {
    $this->apiClient->setBaseUrl("http://127.0.0.1:{$port}/wechat/httpapi");
    $final_message = $is_auto_reply ? "哎哟，{$message}" : $message;
    $result = $this->apiClient->sendText($wxid_room, $final_message);

    return [
      'success' => $result && isset($result['code']) && $result['code'] === 200,
      'message' => $result && isset($result['code']) && $result['code'] === 200 ? 'Message sent successfully' : 'Failed to send message',
      'send_result' => $result,
    ];
  }

  /**
   * Sends image to WeChat group.
   *
   * @param string $wxid_room Group WeChat ID
   * @param string $image_path Image file path
   * @param int $port API port
   */
  private function sendGroupImage(string $wxid_room, string $image_path, int $port = 6999): array {
    $this->apiClient->setBaseUrl("http://127.0.0.1:{$port}/wechat/httpapi");
    $result = $this->apiClient->sendImage($wxid_room, $image_path);

    return [
      'success' => $result && isset($result['code']) && $result['code'] === 200,
      'message' => $result && isset($result['code']) && $result['code'] === 200 ? 'Image sent successfully' : 'Failed to send image',
      'send_result' => $result,
    ];
  }

  /**
   * Creates or updates room entity with manager information.
   */
  private function createOrUpdateRoom(string $wxid_room, string $created_wxid, array $manager_wxids = []): array {
    $storage = $this->entityTypeManager->getStorage('room');
    $existing_rooms = $storage->loadByProperties(['title' => $wxid_room]);

    if (!empty($existing_rooms)) {
      $room = reset($existing_rooms);
      $room->set('created_wxid', $created_wxid);

      if (!empty($manager_wxids)) {
        $existing_managers = json_decode($room->get('manager_wxid')->value ?? '[]', TRUE) ?: [];
        $merged_managers = array_values(array_unique(array_merge($existing_managers, $manager_wxids)));
        $room->set('manager_wxid', json_encode($merged_managers));
      }

      $room->set('changed', time());

      return $room->save() ? [
        'success' => TRUE,
        'action' => 'updated',
        'message' => '房间信息已更新',
        'room_id' => $room->id(),
        'managers' => $merged_managers ?? [],
      ] : [
        'success' => FALSE,
        'action' => 'update_failed',
        'message' => '房间更新失败',
      ];
    }

    $room = $storage->create([
      'title' => $wxid_room,
      'created_wxid' => $created_wxid,
      'manager_wxid' => json_encode($manager_wxids),
    ]);

    return $room->save() ? [
      'success' => TRUE,
      'action' => 'created',
      'message' => '房间创建成功',
      'room_id' => $room->id(),
      'managers' => $manager_wxids,
    ] : [
      'success' => FALSE,
      'action' => 'create_failed',
      'message' => '房间创建失败',
    ];
  }

  /**
   * Removes managers from room entity.
   */
  private function removeManagersFromRoom(string $wxid_room, array $manager_wxids): array {
    $storage = $this->entityTypeManager->getStorage('room');
    $existing_rooms = $storage->loadByProperties(['title' => $wxid_room]);

    if (empty($existing_rooms)) {
      return [
        'success' => FALSE,
        'action' => 'room_not_found',
        'message' => '房间不存在',
      ];
    }

    $room = reset($existing_rooms);
    $existing_managers = json_decode($room->get('manager_wxid')->value ?? '[]', TRUE) ?: [];
    $updated_managers = array_values(array_diff($existing_managers, $manager_wxids));

    $room->set('manager_wxid', json_encode($updated_managers));
    $room->set('changed', time());

    return $room->save() ? [
      'success' => TRUE,
      'action' => 'managers_removed',
      'message' => '管理员已移除',
      'room_id' => $room->id(),
      'managers' => $updated_managers,
    ] : [
      'success' => FALSE,
      'action' => 'remove_failed',
      'message' => '移除管理员失败',
    ];
  }

  private function isManager(string $wxid_room, string $wxid): bool {
    $storage = $this->entityTypeManager->getStorage('room');
    $rooms = $storage->loadByProperties(['title' => $wxid_room]);

    if (empty($rooms)) {
      return FALSE;
    }

    $room = reset($rooms);
    $created_wxid = $room->get('created_wxid')->value ?? '';

    if ($created_wxid === $wxid) {
      return TRUE;
    }

    $manager_wxids = json_decode($room->get('manager_wxid')->value ?? '[]', TRUE) ?: [];
    return in_array($wxid, $manager_wxids);
  }

  /**
   * Updates game status for the room.
   */
  private function updateGameStatus(string $wxid_room, int $status): array {
    $storage = $this->entityTypeManager->getStorage('room');
    $rooms = $storage->loadByProperties(['title' => $wxid_room]);

    if (empty($rooms)) {
      return [
        'success' => FALSE,
        'message' => '房间不存在',
        'action' => 'room_not_found',
      ];
    }

    $room = reset($rooms);
    $old_status = $room->get('status_game')->value ?? 0;

    // Update game status
    $room->set('status_game', $status);
    $room->set('changed', time());

    if ($room->save()) {
      $result = [
        'success' => TRUE,
        'message' => '游戏状态已更新',
        'action' => 'status_updated',
        'room_id' => $room->id(),
        'old_status' => $old_status,
        'new_status' => $status,
      ];

      // 如果是结束游戏（status=0），更新所有wager的status_round为2
      if ($status === 0) {
        $wager_update_result = $this->updateWagerRoundStatus($wxid_room, 2);
        $result['wager_update'] = $wager_update_result;
      }

      return $result;
    }

    return [
      'success' => FALSE,
      'message' => '游戏状态更新失败',
      'action' => 'update_failed',
    ];
  }

  private function updateWagerRoundStatus(string $wxid_room, int $status_round): array {
    $storage = $this->entityTypeManager->getStorage('wager');
    if (!$storage) {
      return [
        'success' => FALSE,
        'message' => '无法获取wager存储服务',
        'updated_count' => 0,
      ];
    }

    // 查询当前群组的所有wager记录
    $wagers = $storage->loadByProperties(['wxid_room' => $wxid_room]);
    if (empty($wagers)) {
      return [
        'success' => TRUE,
        'message' => '当前群组没有需要更新的wager记录',
        'updated_count' => 0,
      ];
    }

    $updated_count = 0;
    foreach ($wagers as $wager) {
      // 更新所有当前群组的wager记录，不管当前状态是什么
      $wager->set('status_round', $status_round);
      $wager->set('changed', time());
      if ($wager->save()) {
        $updated_count++;
      }
    }

    return [
      'success' => TRUE,
      'message' => "已更新{$updated_count}条wager记录的轮次状态（全局更新）",
      'updated_count' => $updated_count,
      'target_status' => $status_round,
    ];
  }

  /**
   * Processes game control commands from managers.
   */
  private function processGameControlCommand(array $data): array {
    $message_data = $data['data'] ?? [];
    $port = $data['port'] ?? 6999;

    // Check if message is from group
    if (($message_data['fromType'] ?? 0) !== 2) {
      return ['status' => 'ignored', 'reason' => 'Not a group message'];
    }

    $message = $message_data['msg'] ?? '';
    $wxid_room = $message_data['fromWxid'] ?? '';
    $sender_wxid = $message_data['finalFromWxid'] ?? '';

    // Check if sender is manager
    if (empty($wxid_room) || empty($sender_wxid)) {
      return ['status' => 'ignored', 'reason' => 'Missing group or sender wxid'];
    }

    if (!$this->isManager($wxid_room, $sender_wxid)) {
      $this->logger->info('Non-manager attempted game control: @sender in group: @group', [
        '@sender' => $sender_wxid,
        '@group' => $wxid_room,
      ]);
      return ['status' => 'ignored', 'reason' => 'Not a manager'];
    }

    // Check for game control keywords
    $is_start_command = $this->containsKeywords($message, 'game_start');
    $is_end_command = $this->containsKeywords($message, 'game_end');

    if (!$is_start_command && !$is_end_command) {
      return ['status' => 'ignored', 'reason' => 'Not a valid game control command'];
    }

    // Update game status
    $new_status = $is_start_command ? 1 : 0;
    $status_result = $this->updateGameStatus($wxid_room, $new_status);

    if (!$status_result['success']) {
      return [
        'status' => 'error',
        'reason' => 'Failed to update game status',
        'result' => $status_result,
      ];
    }

    // Send response message
    $action = $is_start_command ? '开始' : '结束';
    $response_message = $is_start_command ? '请开始吧' : '结束了哦';
    $send_result = $this->sendGroupMessage($wxid_room, $response_message, $port);

    if ($send_result['success']) {
      $this->logger->info('Game @action command executed by manager: @sender in group: @group', [
        '@action' => $action,
        '@sender' => $sender_wxid,
        '@group' => $wxid_room,
      ]);

      return [
        'status' => 'success',
        'message' => "Game {$action} command executed",
        'wxid_room' => $wxid_room,
        'sender_wxid' => $sender_wxid,
        'game_status' => $new_status,
        'send_result' => $send_result,
      ];
    }

    return [
      'status' => 'error',
      'reason' => 'Failed to send response message',
      'send_result' => $send_result,
    ];
  }

  /**
   * Processes admin management commands (set/remove managers).
   */
  private function processAdminSetupTrigger(array $data): array {
    $message_data = $data['data'] ?? [];
    $port = $data['port'] ?? 6999;

    $this->apiClient->setBaseUrl("http://127.0.0.1:{$port}/wechat/httpapi");

    if (!$this->isMessageFromSelf($message_data) || ($message_data['fromType'] ?? 0) !== 2) {
      return ['status' => 'ignored', 'reason' => 'Invalid message source or type'];
    }

    $message = $message_data['msg'] ?? '';
    $is_set_admin = $this->isAdminCommand($message, 'admin_set');
    $is_remove_admin = $this->isAdminCommand($message, 'admin_remove');

    if (!$is_set_admin && !$is_remove_admin) {
      return ['status' => 'ignored', 'reason' => 'Not an admin management command'];
    }

    $at_wxid_list = $message_data['atWxidList'] ?? [];
    if (empty($at_wxid_list)) {
      $wxid_room = $message_data['fromWxid'] ?? '';
      if (!empty($wxid_room)) {
        $example = $is_set_admin ? '@张三 设置管理员' : '@张三 移除管理员';
        $this->sendGroupMessage($wxid_room, "请@需要的人员\r示例：{$example}", $port);
      }
      return ['status' => 'ignored', 'reason' => 'No mentioned users'];
    }

    $wxid_room = $message_data['fromWxid'] ?? '';
    $info = $this->getCurrentWechatInfo();
    $current_wxid = $info['result']['wxid'] ?? NULL;

    if (empty($wxid_room) || !$current_wxid) {
      return ['status' => 'error', 'reason' => 'Missing required data'];
    }

    $room_result = $is_set_admin
      ? $this->createOrUpdateRoom($wxid_room, $current_wxid, $at_wxid_list)
      : $this->removeManagersFromRoom($wxid_room, $at_wxid_list);

    $manager_count = count($room_result['managers'] ?? []);
    $action = $is_set_admin ? 'set' : 'remove';

    if ($is_set_admin) {
      $action_text = $room_result['action'] === 'created' ? '新建' : '更新';
      $message = $room_result['success']
        ? "🎉 管理员设置完成！\r✅ 房间{$action_text}成功\r房间ID: {$room_result['room_id']}\r管理员数量: {$manager_count}"
        : "🎉 管理员设置完成！\r❌ 房间操作失败: {$room_result['message']}";
    } else {
      $message = $room_result['success']
        ? "🎉 管理员移除完成！\r✅ 管理员已移除\r房间ID: {$room_result['room_id']}\r剩余管理员数量: {$manager_count}"
        : "🎉 管理员移除完成！\r❌ 操作失败: {$room_result['message']}";
    }

    $send_result = $this->sendGroupMessage($wxid_room, $message, $port);

    if ($send_result['success']) {
      $this->logger->info('Admin management completed for group: @group, Action: @action', [
        '@group' => $wxid_room,
        '@action' => $action,
      ]);

      return [
        'status' => 'success',
        'message' => 'Admin management completed',
        'wxid_room' => $wxid_room,
        'action' => $action,
        'room_result' => $room_result,
        'send_result' => $send_result,
      ];
    }

    return [
      'status' => 'error',
      'reason' => 'Failed to send completion message',
      'room_result' => $room_result,
      'send_result' => $send_result,
    ];
  }

  /**
   * Handles WeChat framework injection success event.
   */
  public function handleInjectSuccess(array $data): array {
    $port = $data['port'] ?? 0;
    if ($port > 0) {
      $this->apiClient->setBaseUrl("http://127.0.0.1:{$port}/wechat/httpapi");
    }

    return [
      'status' => 'success',
      'message' => 'Injection success handled',
      'data' => [
        'port' => $port,
        'pid' => $data['data']['pid'] ?? 0,
        'flag' => $data['flag'] ?? '',
      ],
    ];
  }

  /**
   * Handles WeChat login success event.
   */
  public function handleLoginSuccess(array $data): array {
    return [
      'status' => 'success',
      'message' => 'Login success handled',
      'data' => [
        'wxid' => $data['wxid'] ?? '',
        'nick' => $data['data']['nick'] ?? '',
        'phone' => $data['data']['phone'] ?? '',
      ],
    ];
  }



  /**
   * Handles received message events.
   */
  public function handleRecvMsg(array $data): array {
    $message_data = $data['data'] ?? [];
    $message = $message_data['msg'] ?? '';

    if (strpos($message, '哎哟，') === 0) {
      return ['status' => 'ignored', 'reason' => 'Auto-reply message ignored'];
    }

    // 按优先级处理消息，一旦有函数处理了就停止
    $result = $this->processAdminSetupTrigger($data);
    if ($result['status'] !== 'ignored') {
      return $result;
    }

    $result = $this->processGameControlCommand($data);
    if ($result['status'] !== 'ignored') {
      return $result;
    }

    $result = $this->processStatisticsCommand($data);
    if ($result['status'] !== 'ignored') {
      return $result;
    }

    $result = $this->processWagerCommand($data);
    if ($result['status'] !== 'ignored') {
      return $result;
    }

    return ['status' => 'ignored', 'message' => 'No handler processed the message'];
  }

  /**
   * Handles transfer payment events.
   */
  public function handleTransPay(array $data): array {
    return ['status' => 'success', 'message' => 'Transfer event handled'];
  }

  /**
   * Handles message revoke events.
   */
  public function handleRevokeMsg(array $data): array {
    return ['status' => 'success', 'message' => 'Revoke event handled'];
  }

  /**
   * Handles friend request events.
   */
  public function handleFriendReq(array $data): array {
    return ['status' => 'success', 'message' => 'Friend request handled'];
  }

  /**
   * Handles authorization expiration events.
   */
  public function handleAuthExpire(array $data): array {
    return ['status' => 'success', 'message' => 'Auth expire handled'];
  }

  /**
   * Handles QR code payment events.
   */
  public function handleQrPay(array $data): array {
    return ['status' => 'success', 'message' => 'QR pay event handled'];
  }

  /**
   * Handles group member changes events.
   */
  public function handleGroupMemberChanges(array $data): array {
    return ['status' => 'success', 'message' => 'Group member changes handled'];
  }

}
